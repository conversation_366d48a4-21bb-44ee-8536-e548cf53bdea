using EngagetoEntities.Entities;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoContracts.CacheContracts;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.Services
{
    public class ConversationCacheService : IConversationCacheService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<ConversationCacheService> _logger;
        private readonly IConfiguration _configuration;
        public ConversationCacheService(
            IServiceProvider serviceProvider,
            IMemoryCache memoryCache,
            IConfiguration configuration,
            ILogger<ConversationCacheService> logger)
        {
            _serviceProvider = serviceProvider;
            _memoryCache = memoryCache;
            _logger = logger;
            _configuration = configuration;
        }
        public async Task<Conversations?> GetConversationAsync(string waMessageId)
        {
            if (string.IsNullOrWhiteSpace(waMessageId))
            {
                return null;
            }
            try
            {
                if (_memoryCache.TryGetValue(waMessageId, out Conversations? cachedConversation))
                {
                    return cachedConversation;
                }
                var conversation = await GetConversationFromDatabase(waMessageId);
                if (conversation != null)
                {
                    _memoryCache.Set(waMessageId, conversation, CreateCacheOptions());
                }
           
                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversation for waMessageId: {WaMessageId}", waMessageId);
                return null;
            }
        }
        public async Task UpdateCacheAsync(string waMessageId, Conversations conversation)
        {
            if (string.IsNullOrWhiteSpace(waMessageId))
            {
                return;
            }
            if (conversation == null)
            {
                return;
            }
            try
            {
                _memoryCache.Set(waMessageId, conversation, CreateCacheOptions());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cache for waMessageId: {WaMessageId}", waMessageId);
            }
        }
        private MemoryCacheEntryOptions CreateCacheOptions()
        {
            var cacheExpirationMinutes = _configuration.GetValue<int>("CacheSettings:ConversationCacheExpirationMinutes");
            var _cacheExpiration = TimeSpan.FromMinutes(cacheExpirationMinutes);
            return new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(_cacheExpiration)
                .SetPriority(CacheItemPriority.High);
        }
        private async Task<Conversations?> GetConversationFromDatabase(string waMessageId)
        {
            using var scope = _serviceProvider.CreateScope();
            var genericRepository = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

            var conversations = await genericRepository.GetByObjectAsync<Conversations>(
                new Dictionary<string, object> { { "WhatsAppMessageId", waMessageId } });

            return conversations?.FirstOrDefault();
        }
    }
}
