using EngagetoEntities.Entities;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoContracts.CacheContracts;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.Services
{
    public class CacheService : ICacheService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<CacheService> _logger;
        private readonly IConfiguration _configuration;
        public CacheService(
            IServiceProvider serviceProvider,
            IMemoryCache memoryCache,
            IConfiguration configuration,
            ILogger<CacheService> logger)
        {
            _serviceProvider = serviceProvider;
            _memoryCache = memoryCache;
            _logger = logger;
            _configuration = configuration;
        }
        public async Task<Conversations?> GetConversationAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return null;
            }
            try
            {
                if (_memoryCache.TryGetValue(key, out Conversations? cachedConversation))
                {
                    return cachedConversation;
                }
                var conversation = await GetConversationFromDatabase(key);
                if (conversation != null)
                {
                    _memoryCache.Set(key, conversation, CreateCacheOptions());
                }
           
                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversation for waMessageId: {WaMessageId}", key);
                return null;
            }
        }
        public async Task UpdateCacheAsync(string key, Object value)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return;
            }
            if (value == null)
            {
                return;
            }
            try
            {
                _memoryCache.Set(key, value, CreateCacheOptions());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cache for waMessageId: {WaMessageId}", key);
            }
        }
        private MemoryCacheEntryOptions CreateCacheOptions()
        {
            var cacheExpirationMinutes = _configuration.GetValue<int>("CacheSettings:CacheExpirationTime");
            var _cacheExpiration = TimeSpan.FromMinutes(cacheExpirationMinutes);
            return new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(_cacheExpiration)
                .SetPriority(CacheItemPriority.High);
        }
        private async Task<Conversations?> GetConversationFromDatabase(string key)
        {
            using var scope = _serviceProvider.CreateScope();
            var genericRepository = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

            var conversations = await genericRepository.GetByObjectAsync<Conversations>(
                new Dictionary<string, object> { { "WhatsAppMessageId", key } });

            return conversations?.FirstOrDefault();
        }
    }
}
