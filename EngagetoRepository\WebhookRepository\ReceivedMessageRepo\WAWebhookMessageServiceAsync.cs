﻿using Engageto.Hubs;
using EngagetoBackGroundJobs.Implementation;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.OptinContracts;
using EngagetoContracts.Services;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoRepository.MetaServices;
using EngagetoContracts.CacheContracts;
using Mapster;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class WAWebhookMessageServiceAsync : IWAWebhookMessageServiceAsync
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IConversationService _conversationService;
        private readonly IMediaURL _mediaURL;
        private readonly IOptin _optin;
        private readonly IWebhookService _webhookService;
        private readonly IWAWebhookHelper _wAWebhookHelper;
        private readonly ApplicationDbContext _applicationDbContext;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly string _terminateJobUrl;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _conversationAnalyticsService;
        private IHubContext<MessageHub, IMessageHubClient> _messageHub;
        private readonly IWAAutoReplyMessageProcess _wAAutoReplyMessage;
        private readonly LeadProcessingService _leadProcessingService;
        private readonly IAutomationSettingService _automationSettingService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IClientDetailsService _clientDetailsService;
        private readonly IEmailService _emailService;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILeadgenIntegrationService _leadgenIntegrationService;
        private readonly IInboxRepository _inboxRepository;
        private readonly IMetaCostInfoService _metaCostInfoService;
        private readonly ILogger<WAWebhookMessageServiceAsync> _logger;
        private readonly IEnvironmentService _environmentService;
        private readonly IConversationCacheService _conversationCacheService;

        public WAWebhookMessageServiceAsync(IGenericRepository genericRepository,
            IContactRepositoryBase contactRepository,
            IConversationService conversationService,
            ApplicationDbContext applicationDbContext,
            IMediaURL mediaURL,
            IOptin optin,
            IWebhookService webhookService,
            IWAWebhookHelper wAWebhookHelper,
            IHubContext<MessageHub, IMessageHubClient> messageHub,
            IWAAutoReplyMessageProcess wAAutoReplyMessage,
            LeadProcessingService leadProcessingService,
            IAutomationSettingService automationSettingService,
            ILogHistoryService logHistoryService,
            IClientDetailsService clientDetailsService,
            IEmailService emailService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService conversationAnalyticsService,
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILeadgenIntegrationService leadgenIntegrationService,
            IInboxRepository inboxRepository,
            IMetaCostInfoService metaCostInfoService,
            ILogger<WAWebhookMessageServiceAsync> logger,
            IEnvironmentService environmentService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IConversationCacheService conversationCacheService
            )
        {
            _applicationDbContext = applicationDbContext;
            _genericRepository = genericRepository;
            _conversationService = conversationService;
            _contactRepository = contactRepository;
            _mediaURL = mediaURL;
            _optin = optin;
            _webhookService = webhookService;
            _wAWebhookHelper = wAWebhookHelper;
            _wAAutoReplyMessage = wAAutoReplyMessage;
            _userService = userService;
            _messageHub = messageHub;
            _leadProcessingService = leadProcessingService;
            _automationSettingService = automationSettingService;
            _logHistoryService = logHistoryService;
            _clientDetailsService = clientDetailsService;
            _conversationAnalyticsService = conversationAnalyticsService;
            _emailService = emailService;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _leadgenIntegrationService = leadgenIntegrationService;
            _inboxRepository = inboxRepository;
            _metaCostInfoService = metaCostInfoService;
            _logger = logger;
            _environmentService = environmentService;
            _httpClientFactory = httpClientFactory;
            _conversationCacheService = conversationCacheService;

            // Set the appropriate Azure Function URL based on environment
            if (_environmentService.IsDevelopment)
            {
                _terminateJobUrl = configuration["FunctionSettings:Dev_TerminateJobUrl"] ?? "";
            }
            else
            {
                _terminateJobUrl = configuration["FunctionSettings:Prod_TerminateJobUrl"] ?? "";
            }
        }
        public async Task ProcessWAWebhookMessageAsync(WAWebhookDto webhookDto, CancellationToken token)
        {
            try
            {
                var webhookJson = JsonConvert.SerializeObject(webhookDto);
                _logger.LogInformation("Webhook summary:\n{WebhookSummary}", webhookJson);

                var changes = webhookDto.Entry?[0].Changes?[0];
                var entryValue = webhookDto.Entry?[0];
                var field = changes?.Field;

                _logger.LogInformation("Detected webhook field: {Field}", field);

                switch (field)
                {
                    case "messages":
                        _logger.LogInformation("-> Case: messages\nWebhookDto: {WebhookSummary}", webhookJson);

                        var contactDetails = changes?.Value?.Contacts?.FirstOrDefault();
                        if (contactDetails != null)
                        {
                            _logger.LogInformation("Processing received message for contact: {WaId}", contactDetails.WaId);
                            // You may include webhookJson if needed for this contact log
                            await RunInBackground(() => HandleReceviedMessageAsync(webhookDto, token));
                        }

                        if (changes?.Value?.Statuses != null)
                        {
                            _logger.LogInformation("Processing message status update.");
                            await ProcessSentMessageAsync(webhookDto, token);
                        }
                        break;

                    case "message_template_status_update":
                        _logger.LogInformation("-> Case: message_template_status_update\nWebhookDto: {WebhookSummary}", webhookJson);
                        await RunInBackground(() => HandleReceivedTemplateStatus(webhookDto, "message_template_status_update", token));
                        break;

                    case "business_capability_update":
                        _logger.LogInformation("-> Case: business_capability_update\nWebhookDto: {WebhookSummary}", webhookJson);

                        var waAccountId = entryValue?.Id;
                        long messageLimit = changes?.Value?.MessageLimitPerUser ?? 0;

                        if (waAccountId != null && messageLimit > 0)
                        {
                            _logger.LogInformation("Updating Meta account. WAAccountId: {WAAccountId}, MessageLimit: {MessageLimit}", waAccountId, messageLimit);
                            await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, null, null, null);
                        }
                        else
                        {
                            _logger.LogWarning("Missing waAccountId or messageLimit during business_capability_update.");
                        }
                        break;

                    case "phone_number_quality_update":
                        _logger.LogInformation("-> Case: phone_number_quality_update\nWebhookDto: {WebhookSummary}", webhookJson);

                        waAccountId = entryValue?.Id;
                        messageLimit = 0;
                        string? businessStatus = changes?.Value?.Event;
                        string? tier = changes?.Value?.CurrentLimit;
                        string? displayNumber = changes?.Value?.DisplayPhoneNumber;

                        if (waAccountId != null && businessStatus != null && tier != null && displayNumber != null)
                        {
                            _logger.LogInformation("Updating phone number quality. WAAccountId: {WAAccountId}, Tier: {Tier}, Status: {Status}, Number: {Number}",
                                waAccountId, tier, businessStatus, displayNumber);

                            await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, businessStatus, tier, displayNumber);
                        }
                        else
                        {
                            _logger.LogWarning("Missing values for phone_number_quality_update.");
                        }
                        break;

                    case "message_template_quality_update":
                        _logger.LogInformation("-> Case: message_template_quality_update\nWebhookDto: {WebhookSummary}", webhookJson);
                        await RunInBackground(() => HandleReceivedTemplateStatus(webhookDto, "message_template_status_update", token));
                        break;

                    default:
                        _logger.LogWarning("Unhandled field type: {Field}", field);
                        _logger.LogInformation("WebhookDto summary (unhandled): {WebhookSummary}", webhookJson);
                        break;
                }

                _logger.LogInformation("=== WA Webhook Processing Complete for Field: {Field} ===", field);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ProcessWAWebhookMessageAsync");
                await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWAWebhookMessageAsync:Error", webhookDto, JsonConvert.SerializeObject(ex), "process webhook");
            }
        }

        public async Task ProcessSentMessageAsync(WAWebhookDto message, CancellationToken token)
        {
            try
            {
                _logger.LogInformation($"Processing WA message webhook...{JsonConvert.SerializeObject(message)}");

                var value = message.Entry?[0].Changes?[0]?.Value;
                if (value?.Statuses == null)
                {
                    _logger.LogWarning("No statuses found in the incoming webhook payload.");
                    return;
                }

                var status = value.Statuses.FirstOrDefault()?.Status;
                var waMessageId = value.Statuses.FirstOrDefault()?.Id;
                _logger.LogInformation("Received status '{Status}' for WA message ID: {MessageId}", status, waMessageId);


                var existingConversation = await _conversationCacheService.GetConversationAsync(waMessageId);

                if (existingConversation == null)
                {
                    _logger.LogWarning("No conversation found for WA message ID: {MessageId}", waMessageId);
                    return;
                }
                var newStatus = GetConvStatusFromString(status);
                if (!ShouldUpdateStatus(existingConversation.Status, newStatus))
                {
                    _logger.LogInformation("Status update ignored due to precedence. Current: {CurrentStatus}, Incoming: {IncomingStatus}",
                        existingConversation.Status, newStatus);
                    return;
                }

                _logger.LogInformation("Status update approved. Current: {CurrentStatus}, New: {NewStatus}",
                    existingConversation.Status, newStatus);

                var metaData = value?.Metadata;

                // Check if message is a campaign using biz_opaque_callback_data field
                bool isCampaign = false;
                var bizOpaqueData = value.Statuses.FirstOrDefault()?.BizOpaqueCallBackData;
                if (!string.IsNullOrEmpty(bizOpaqueData))
                {
                    isCampaign = true;
                    _logger.LogInformation("Campaign detected from biz_opaque_callback_data: {IsCampaign} for WA message ID: {MessageId}", isCampaign, waMessageId);
                }

                _logger.LogInformation("IsCampaign: {IsCampaign} for WA message ID: {MessageId}", isCampaign, waMessageId);

                Conversations conversation = new();
                string @event = string.Empty;

                switch (status)
                {
                    case "sent":
                        @event = "UpdateConversationSentMessage";
                        _logger.LogInformation("Updating conversation and campaign tracker for status: sent");
                        conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.sent);

                        // Update cache with the conversation object we just got from DB (no additional DB call)
                        await _conversationCacheService.UpdateCacheAsync(waMessageId, conversation);
                        _logger.LogInformation("Cache updated after status update to: sent");

                        var pricing = value.Statuses[0].Pricing;
                        if (pricing != null)
                        {
                            _logger.LogInformation("Pricing info found. Processing Meta cost deduction...");
                            await MetaCostHandlerAsync(message, conversation.From);
                        }
                        break;

                    case "delivered":
                        @event = "UpdateConversationDeliveredMessage";
                        _logger.LogInformation("Updating conversation and campaign tracker for status: delivered");
                        conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.delivered);

                        // Update cache with the conversation object we just got from DB (no additional DB call)
                        await _conversationCacheService.UpdateCacheAsync(waMessageId, conversation);
                        _logger.LogInformation("Cache updated after status update to: delivered");

                        pricing = value.Statuses[0].Pricing;
                        if (pricing != null)
                        {
                            _logger.LogInformation("Pricing info found. Processing Meta cost deduction...");
                            await MetaCostHandlerAsync(message, conversation.From);
                        }

                        if (!isCampaign)
                        {
                            _logger.LogInformation($"Triggering LeadGen integration for Delivered event:{JsonConvert.SerializeObject(message)}");
                            await _leadgenIntegrationService.ProcessIntegrationForLeadAsync(
                                new IntegrationAccountRecord(
                                    conversation.From ?? string.Empty, null, conversation, null, null,
                                    new List<IntegrationEvent> { IntegrationEvent.Delivered }));
                        }
                        break;

                    case "read":
                        @event = "UpdateConversationReadMessage";
                        _logger.LogInformation("Updating conversation and campaign tracker for status: read");
                        conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.read);

                        // Update cache with the conversation object we just got from DB (no additional DB call)
                        await _conversationCacheService.UpdateCacheAsync(waMessageId, conversation);
                        _logger.LogInformation("Cache updated after status update to: read");
                        break;

                    case "failed":
                        @event = "UpdateConversationFaildMessage";
                        var errorMessage = value.Statuses.FirstOrDefault()?.Errors?.FirstOrDefault()?.ErrorData?.Details;
                        var error = JsonConvert.SerializeObject(value.Statuses.FirstOrDefault()?.Errors);
                        _logger.LogWarning("Message failed. Error: {ErrorMessage}", errorMessage);
                        conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(
                            waMessageId, ConvStatus.failed, errorMessage, error);

                        // Update cache with the conversation object we just got from DB (no additional DB call)
                        await _conversationCacheService.UpdateCacheAsync(waMessageId, conversation);
                        _logger.LogInformation("Cache updated after status update to: failed");
                        break;

                    default:
                        _logger.LogWarning("Unhandled message status: {Status}", status);
                        return;
                }

                if (!isCampaign)
                {
                    Guid parsedBusinessId = Guid.TryParse(conversation.From, out var tempGuid) ? tempGuid : Guid.Empty;
                    conversation.BusinessId = parsedBusinessId;

                    var contact = _applicationDbContext.Contacts.FirstOrDefault(i =>
                        i.BusinessId == parsedBusinessId &&
                        (i.CountryCode + i.Contact).Replace("+", "") == conversation.To.Replace("+", ""));

                    conversation.ContactId = contact?.ContactId ?? Guid.Empty;

                    _logger.LogInformation("Sending conversation update to client and saving webhook event. BusinessId: {BusinessId}", parsedBusinessId);

                    await SendConvMessageOnServer(conversation.From, new List<Conversations> { conversation }, false);
                    await _webhookService.SaveWebhookEventsAsync(conversation.From, @event, conversation ?? new());
                }

                _logger.LogInformation("WA message processing completed for ID: {MessageId}", waMessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while processing WA message.");
            }
        }


        #region Helper Method
        private async Task RunInBackground(Func<Task> action)
        {
            try
            {
                await Task.Run(action);
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("RunInBackground:Error", JsonConvert.SerializeObject(action), JsonConvert.SerializeObject(ex), "action");
                Console.WriteLine(ex.Message); ;
            }
        }
        private bool ShouldUpdateStatus(ConvStatus currentStatus, ConvStatus newStatus)
        {
            // Failed status always overrides any other status
            if (newStatus == ConvStatus.failed)
            {
                return true;
            }
            // If current status is failed, don't update unless new status is also failed
            if (currentStatus == ConvStatus.failed)
            {
                return false;
            }
            var statusPrecedence = new Dictionary<ConvStatus, int>
            {
                { ConvStatus.sent, 1 },
                { ConvStatus.delivered, 2 },
                { ConvStatus.read, 3 },
                { ConvStatus.failed, 4 } // Highest precedence
            };

            var currentPrecedence = statusPrecedence.GetValueOrDefault(currentStatus, 0);
            var newPrecedence = statusPrecedence.GetValueOrDefault(newStatus, 0);

            // Only allow update if new status has higher or equal precedence
            bool shouldUpdate = newPrecedence > currentPrecedence;

            _logger.LogInformation("Status precedence check: Current: {CurrentStatus} (precedence: {CurrentPrecedence}), " +
                "New: {NewStatus} (precedence: {NewPrecedence}), Should update: {ShouldUpdate}",
                currentStatus, currentPrecedence, newStatus, newPrecedence, shouldUpdate);

            return shouldUpdate;
        }
        private ConvStatus GetConvStatusFromString(string status)
        {
            return status?.ToLowerInvariant() switch
            {
                "sent" => ConvStatus.sent,
                "delivered" => ConvStatus.delivered,
                "read" => ConvStatus.read,
                "failed" => ConvStatus.failed,
                _ => ConvStatus.sent
            };
        }
        public async Task HandleReceivedTemplateStatus(WAWebhookDto message, string? field, CancellationToken token)
        {
            field = message.Entry?[0]?.Changes[0]?.Field;
            var value = message.Entry[0]?.Changes[0]?.Value;
            var status = value.Event;
            var templateId = value.MessageTemplateId;
            var reasion = value.Reason;
            var template = (await _genericRepository.GetByObjectAsync<Template>(new() { { "MetaId", templateId } }, "Templates"))?.FirstOrDefault();
            if (template != null)
            {
                switch (field)
                {
                    case "message_template_status_update":
                        //template.Error = reasion;
                        template.StatusReason = reasion;
                        switch (status?.ToLowerInvariant())
                        {
                            case string s when s.Contains("approved"):
                                template.Status = WATemplateStatus.APPROVED;
                                await RunInBackground(() => _webhookService.SendTemplateWebhookAsync(template.BusinessId, template.TemplateId));
                                var waTemplate = template.Adapt<WebhookResponseTemplateDto>();
                                await RunInBackground(() => _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord
                                    (
                                        template.BusinessId, null, null, waTemplate, null, new List<IntegrationEvent> { IntegrationEvent.Template }), CancellationToken.None
                                    ));
                                break;
                            case string s when s.Contains("rejected"):
                                template.Status = WATemplateStatus.REJECTED;
                                break;
                            case string s when s.Contains("flagged"):
                                template.Status = WATemplateStatus.FLAGGED;
                                break;
                            case string s when s.Contains("in_appeal"):
                                template.Status = WATemplateStatus.IN_APPEAL;
                                break;
                        }
                        break;

                    case "message_template_quality_update":
                        await _emailService.SendEmailViaUtility("Template Quility", JsonConvert.SerializeObject(value), null, new List<string> { "<EMAIL>", "<EMAIL>" }, null, null);
                        var ratingObject = new QualityScoreDtos
                        {
                            PreviousScore = GetQualityScoreEnum(value.PreviousQualityScore),
                            NewScore = GetQualityScoreEnum(value.NewQualityScore),
                            CreatedAt = DateTime.UtcNow,
                        };

                        var existingRatings = string.IsNullOrEmpty(template.Rating)
                            ? new List<QualityScoreDtos>()
                            : JsonConvert.DeserializeObject<List<QualityScoreDtos>>(template.Rating);

                        existingRatings?.Add(ratingObject);

                        SendQualityRatingEmail(value.PreviousQualityScore, value.NewQualityScore, template.BusinessId);

                        break;
                }
                _applicationDbContext.Templates.Update(template);
                await _applicationDbContext.SaveChangesAsync();
                //await _webhookService.SendTemplateWebhookAsync(template.BusinessId, template.TemplateId);
                //await _genericRepository.UpdateRecordAsync("Templates", StringHelpers.GetPropertyNames<TemplateDto>(), template, new Dictionary<string, object> { { "TemplateId", template.TemplateId } });
            }
        }
        private QualityScore GetQualityScoreEnum(string score)
        {
            return score.ToLower() switch
            {
                "green" => QualityScore.GREEN,
                "yellow" => QualityScore.YELLOW,
                "red" => QualityScore.RED,
                _ => QualityScore.UNKNOWN,
            };
        }
        #region Received Process

        private async Task<Contacts> ProcessContactAsync(EngagetoEntities.Dtos.WebhookDtos.Contact contact, string businessId)
        {
            var countryCodeAndPhoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(string.Concat("+", contact.WaId));
            //    var apiKeyEntity =  await _applicationDbContext.ApiKeyEntities.FirstOrDefaultAsync(i=>i.CompanyId== businessId);
            //   var source = apiKeyEntity.Source;

            // var source = await _applicationDbContext.ApiKeyEntities.Where(i => i.CompanyId == businessId).Select(i => i.Source).FirstOrDefaultAsync();
            var source = SourceType.WhatsApp;
            var contactDetails = await _contactRepository.SaveContactNumber(countryCodeAndPhoneNumber.CountryCode, countryCodeAndPhoneNumber.NationalNumber, businessId, source, contact.Profile?.Name);
            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId, contactDetails, null, null, null, new List<IntegrationEvent> { IntegrationEvent.LeadGen }), CancellationToken.None);
            return contactDetails;
        }

        public async Task HandleReceviedMessageAsync(WAWebhookDto webhookDto, CancellationToken token)
        {
            var metaData = webhookDto.Entry?[0].Changes?[0].Value?.Metadata;
            var contactDetails = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Contacts?.FirstOrDefault();
            var message = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Messages?.FirstOrDefault();
            //declare variables
            string? type = message?.Type;
            string textMessage = string.Empty;
            string url = string.Empty;
            string mimeType = string.Empty;
            string caption = string.Empty;
            string? replyId = message?.MessageContext?.Id;

            var businessId = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object> { { "PhoneNumberID", metaData.PhoneNumberId } }))
                            ?.FirstOrDefault()?.BusinessId;

            if (!(await _genericRepository.IsExistAsync<Conversations>(new Dictionary<string, object> { { "WhatsAppMessageId", message.Id } })))
            {
                if (businessId != null)
                {
                    try
                    {
                        var contact = await ProcessContactAsync(contactDetails, businessId);
                        if (contact != null)
                        {
                            // Use helper method to retrieve URL, mime type, and caption
                            (textMessage, url, mimeType, caption, replyId) = await GetMediaDetailsAsync(textMessage, type, message, metaData.PhoneNumberId, replyId);
                            // Save conversation data based on message type
                            var conv = await _conversationService.SaveTextMediaConversationAsync(textMessage, contactDetails.WaId, businessId.ToLowerInvariant(), type, caption, mimeType, url, message.Id, replyId, ConvStatus.received);
                            conv = conv.Clone() as Conversations;
                            await SendConvMessageOnServer(businessId, new() { conv }, true);
                            conv.Status = ConvStatus.received;
                            #region OptInAndOut process#
                            bool isOptInAndOutResult = await _optin.OptInAndOutKeywordProcessAsync(webhookDto, businessId);
                            #endregion
                            #region delay response
                            if (!string.IsNullOrEmpty(contact?.DelayResponseJobID))
                            {
                                await DeleteJobAsync(contact.DelayResponseJobID);
                                contact.DelayResponseJobID = null;
                                _applicationDbContext.Contacts.Update(contact);
                                await _applicationDbContext.SaveChangesAsync();
                            }
                            #endregion
                            #region Autoreply operation
                            await RunInBackground(() => _wAAutoReplyMessage.SendAutoReplyMessageAsync(conv, businessId));
                            #endregion

                            if (contact != null)
                            {
                                bool IsDevelpoment = _environmentService.IsDevelopment;
                                await RunInBackground(() => _nodeWorkflowEngineService.ProcessWorkflowAsync(contact, textMessage, false));
                            }
                            #region Inbox setting operation
                            if (message.Type != "button" && message.Type != "interactive" && !isOptInAndOutResult && contact.IsCompletedWorkflow)
                            {
                                await RunInBackground(() => _wAWebhookHelper.InboxSettingMessageAsync(businessId, contact.Contact, contact.CountryCode));
                            }
                            if (contact.WorkFlowNodeId == null && contact.WorkflowId == null)
                            {
                                contact.IsCompletedWorkflow = true;
                                await _applicationDbContext.SaveChangesAsync();
                            }

                            #endregion
                            #region ChatStatus update
                            if (contact?.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await RunInBackground(() => _wAWebhookHelper.UpdateContactChatStatusAsync(contact));
                            }
                            #endregion
                            #region Lead gen processing
                            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId, contact, conv, null, null, new List<IntegrationEvent> { IntegrationEvent.OneTimeLeadGen, IntegrationEvent.Received, IntegrationEvent.OneTimeReceived }), token);
                            //await _automationSettingService.SendAutomationMessageAsync(businessId, contact, conv, Guid.Empty);
                            #endregion
                            #region send conversation event message by webhook
                            await _webhookService.SaveWebhookEventsAsync(businessId, "Received Message", conv);
                            #endregion

                            #region new automation setting
                            //if (message.Type != "button" && message.Type != "interactive" && !isOptInAndOutResult)
                            //{
                            await RunInBackground(() => _automationSettingService.SendAutomationMessageAsync(businessId, contact, conv, null));
                            //}
                            #endregion


                        }
                    }
                    catch (Exception ex)
                    {
                        await _logHistoryService.SaveInformationLogHistoryAsyn("HandleReceviedMessageAsync:Error", JsonConvert.SerializeObject(webhookDto), JsonConvert.SerializeObject(ex), "processing received message");
                        Console.WriteLine($"Error: {ex.Message}");
                    }
                }
            }
        }

        private async Task<(string textMessage, string url, string mimeType, string caption, string? replyId)> GetMediaDetailsAsync(
            string textMessage,
            string type,
            Message message,
            string phoneNumberId,
            string? replyId)
        {
            string url = string.Empty;
            string mimeType = string.Empty;
            string caption = string.Empty;

            switch (type)
            {
                case "text":
                    textMessage = message.Text?.Body;
                    break;
                case "reaction":
                    textMessage = message.Reaction?.Emoji;
                    replyId = message.Reaction?.MesssageId;
                    break;
                case "image":
                    url = await _mediaURL.GetByMediaId(message.Image?.Id, phoneNumberId);
                    mimeType = message.Image?.MimeType;
                    caption = message.Image?.Caption;
                    break;
                case "video":
                    url = await _mediaURL.GetByMediaId(message.Video?.Id, phoneNumberId);
                    mimeType = message.Video?.MimeType;
                    caption = message.Video?.Caption;
                    break;
                case "audio":
                    url = await _mediaURL.GetByMediaId(message.Audio?.Id, phoneNumberId);
                    mimeType = message.Audio?.MimeType;
                    caption = message.Audio?.Caption;
                    break;
                case "sticker":
                    url = await _mediaURL.GetByMediaId(message.Sticker?.Id, phoneNumberId);
                    mimeType = message.Sticker?.MimeType;
                    caption = message.Sticker?.Caption;
                    break;
                case "document":
                    url = await _mediaURL.GetByMediaId(message.Document?.Id, phoneNumberId);
                    mimeType = message.Document?.MimeType;
                    caption = message.Document?.Caption;
                    break;
                case "button":
                    textMessage = message.Button.Text;
                    break;
                case "interactive":
                    if (message.Interactive.Type == "button_reply")
                    {
                        textMessage = message.Interactive.ButtonReply.Title;

                    }
                    else if (message.Interactive.Type == "list_reply")
                    {
                        textMessage = message.Interactive.ListReply.Title;
                        //textMessage = (list.Title + "\n" + list.Description);

                    }
                    break;
            }
            return (textMessage, url, mimeType, caption, replyId);
        }
        #endregion

        #region send conversation message by socket
        private async Task SendConvMessageOnServer(string businessId, List<Conversations> conversations, bool? isRenderContact = true)
        {
            if (string.IsNullOrEmpty(businessId) || conversations == null || !conversations.Any())
                return;

            try
            {
                var convMessages = conversations.Adapt<List<ConversationDto>>();
                var replyIds = conversations.Where(c => !string.IsNullOrEmpty(c.ReplyId)).Select(c => c.ReplyId).ToList();

                if (replyIds.Any())
                {
                    var repliesConvs = (await _applicationDbContext.Conversations
                        .Where(c => replyIds.Contains(c.WhatsAppMessageId))
                        .ToListAsync()).Adapt<List<ConversationDto>>();

                    var replyDict = repliesConvs.ToDictionary(r => r.WhatsAppMessageId);
                    convMessages.ForEach(conv =>
                    {
                        if (!string.IsNullOrEmpty(conv.ReplyId) && replyDict.TryGetValue(conv.ReplyId, out var replyConv))
                        {
                            conv.Reply = replyConv;
                        }
                    });
                }
                var users = await _applicationDbContext.Users.Where(u => u.CompanyId == businessId).Select(u => new { u.Id }).ToListAsync();
                var groupPrefix = businessId.ToLower();
                var messageTasks = users.Select(user => _messageHub.Clients.Groups($"{groupPrefix}{user.Id.ToString().ToLower()}").ReceiveMessageFromServer(convMessages));
                var contactTasks = Enumerable.Empty<Task>();
                if (isRenderContact ?? false)
                {
                    contactTasks = users.Select(user =>
                        _messageHub.Clients
                            .Groups($"{groupPrefix}{user.Id.ToString().ToLower()}")
                            .RenderContacts()
                    );
                }
                // Run all tasks concurrently
                await Task.WhenAll(messageTasks.Concat(contactTasks));
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("SendConvMessageOnServer:Error", JsonConvert.SerializeObject(conversations), JsonConvert.SerializeObject(ex), "processing message hub");
                Console.WriteLine($"Error in SendConvMessageOnServer: {ex.Message}");
            }
        }

        #endregion

        #endregion

        #region Conversation analytics update
        private async Task MetaCostHandlerAsync(WAWebhookDto webhookDto, string businessId)
        {
            try
            {
                var status = webhookDto.Entry?[0].Changes?[0]?.Value?.Statuses?[0];
                string? countryCode = PhoneNumberHelper.GetRegionCodeFromPhoneNumber(status.RecipientId);
                if (status?.Pricing?.Billable == true)
                {
                    var pricing = status.Pricing;

                    var metaData = webhookDto.Entry?[0].Changes?[0].Value?.Metadata;

                    string category = webhookDto.Entry?[0].Changes?[0]?.Value?.Statuses?[0].Pricing?.Category ?? string.Empty;

                    var cost = await _metaCostInfoService.GetCostAsync(businessId, status.RecipientId, category);
                    var wallet = (await _genericRepository.GetByObjectAsync<UserWalletEntity>(new() { { nameof(UserWalletEntity.CompanyId), businessId } }))?.FirstOrDefault();
                    ConversationAnalyticsEntity entity = new ConversationAnalyticsEntity()
                    {
                        WAMessageId = status.Id,
                        CompanyId = businessId,
                        Conversation = 1,
                        ConversationCategory = category,
                        ConversationType = "REGULAR",
                        Cost = cost,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        StartDate = DateTime.UtcNow,
                        EndDate = DateTime.UtcNow,
                        MobileNumber = webhookDto?.Entry?[0].Changes?[0]?.Value?.Metadata?.DisplayPhoneNumber,
                        CurrentBalance = (wallet?.Balance ?? 0 - cost),
                        Country = countryCode
                    };
                    var isInsert = await _inboxRepository.SaveConversationCostAsync(entity);
                    if (isInsert && wallet != null)
                    {
                        wallet.Balance = wallet.Balance - cost;
                        wallet.ExpectedWalletBallance -= cost;
                        await _genericRepository.UpdateRecordAsync("UserWalletEntities", StringHelper.GetPropertyNames<UserWalletEntity>(false), wallet, new() { { "CompanyId", wallet.CompanyId } });
                    }
                }
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("MetaCostHandlerAsync:Error", JsonConvert.SerializeObject(webhookDto), JsonConvert.SerializeObject(ex), "Error while updating conversation cost");
            }
        }
        #endregion

        private async void SendQualityRatingEmail(string previousQualityScore, string newQualityScore, string businessId)
        {
            var userDetails = await (
                              from user in _applicationDbContext.Users
                              join role in _applicationDbContext.Roles
                              on user.RoleId equals role.Id.ToString()
                              where user.CompanyId == businessId &&
                              role.CompanyId == businessId &&
                              (role.Name == "owner" || role.Name == "admin")
                              select new { user.EmailAddress, user.Name }).ToListAsync();

            var emails = userDetails.Select(i => i.EmailAddress).ToList();
            var clientName = userDetails.Select(i => i.Name).ToList();

            await _emailService.SendQualityRatingEmail(emails, previousQualityScore, newQualityScore, clientName);
        }

        /// <summary>
        /// Deletes a job using Azure Function TerminateJob
        /// </summary>
        private async Task<bool> DeleteJobAsync(string jobId)
        {
            try
            {
                if (string.IsNullOrEmpty(jobId))
                    return false;

                if (string.IsNullOrWhiteSpace(_terminateJobUrl))
                {
                    throw new InvalidOperationException("Terminate job URL is not configured.");
                }

                var payload = new { JobId = jobId };

                var client = _httpClientFactory.CreateClient();
                var jsonPayload = System.Text.Json.JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(_terminateJobUrl, content);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting job via Azure Function: {JobId}", jobId);
                return false;
            }
        }

    }
}
